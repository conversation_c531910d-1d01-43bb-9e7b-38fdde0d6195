<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="区域名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入区域名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                     :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['product:seo-card:create']">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="区域名称" align="center" prop="name" />
      <el-table-column label="显示标题" align="center" prop="title" />
      <el-table-column label="图标" align="center" prop="icon">
        <template v-slot="scope">
          <el-image v-if="scope.row.icon" style="width: 50px; height: 50px" :src="scope.row.icon" fit="cover"></el-image>
        </template>
      </el-table-column>
      <el-table-column label="背景图" align="center" prop="imageUrl">
        <template v-slot="scope">
          <el-image v-if="scope.row.imageUrl" style="width: 50px; height: 50px" :src="scope.row.imageUrl" fit="cover"></el-image>
        </template>
      </el-table-column>
      <el-table-column label="区域类型" align="center" prop="type">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.PRODUCT_SEO_CARD_TYPE" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="区域布局" align="center" prop="layout">
        <template v-slot="scope">
          <dict-tag :type="DICT_TYPE.PRODUCT_SEO_CARD_LAYOUT" :value="scope.row.layout"/>
        </template>
      </el-table-column> -->
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="状态" key="status" align="center">
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1" @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['product:seo-card:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['product:seo-card:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="800px" v-dialogDrag append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="区域名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入区域名称" />
        </el-form-item>
        <el-form-item label="显示标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入显示标题" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="图标" prop="icon">
              <imageUpload v-model="form.icon" :limit="1" :fileSize="0.1"/>
              <span class="upload-ext-tip">推荐尺寸 30*30</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="背景图" prop="imageUrl">
              <imageUpload v-model="form.imageUrl" :limit="1" :fileSize="0.5"/>
              <span class="upload-ext-tip">推荐尺寸 240*580</span>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="区域布局" prop="layout">
          <el-radio-group v-model="form.layout">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.PRODUCT_SEO_CARD_LAYOUT)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="区域类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.PRODUCT_SEO_CARD_TYPE)"
                      :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="跳转链接" prop="linkUrl" v-if="form.type === 10" :rules="[{type: 'url', message: '链接格式不正确', trigger: 'blur'}]">
          <el-input v-model="form.linkUrl" placeholder="请输入跳转链接" />
        </el-form-item>
        <el-form-item label="商品标签" prop="tags">
          <div style="display: inline-block;">
            <template v-if="form.tags">
              <el-tag v-for="tag in form.tags" :key="tag.id" closable style="margin-left: 10px;" @close="handleTagClose(tag)"
              >{{tag.name}}</el-tag>
            </template>
            <el-button icon="el-icon-plus" @click="addProductTag" circle size="small" style="margin-left: 10px;"></el-button>
          </div>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)"
                          :key="dict.value" :label="parseInt(dict.value)">{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input v-model="form.sort" placeholder="请输入区域排序, 值越大优先级越大" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="memo">
          <el-input v-model="form.memo" placeholder="请输入区域备注" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <TagDialog ref="tagDialog" @on-update="saveTags"></TagDialog>
  </div>
</template>

<script>
import { createSeoCard, updateSeoCard, updateSeoCardStatus, deleteSeoCard, getSeoCard, getSeoCardPage } from "@/api/mall/product/seoCard";
import ImageUpload from '@/components/ImageUpload';
import TagDialog from '@/components/TagDialog'
export default {
  name: "ProductSeoCard",
  components: {
    ImageUpload, TagDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 运营区域列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        title: null,
        type: null,
        status: null,
        createTime: []
      },
      // 表单参数
      form: {},
      contentObj: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "区域名称不能为空", trigger: "blur" }],
        title: [{ required: true, message: "显示标题不能为空", trigger: "blur" }],
        type: [{ required: true, message: "区域类型不能为空", trigger: "change" }],
        layout: [{ required: true, message: "区域布局不能为空", trigger: "blur" }],
        status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getSeoCardPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        title: undefined,
        icon: undefined,
        imageUrl: undefined,
        type: undefined,
        layout: 10,
        content: undefined,
        memo: undefined,
        sort: 10,
        status: 0,
        linkUrl: '',
        tags: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加运营区域";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getSeoCard(id).then(response => {
        let data = response.data
        data.linkUrl = undefined
        data.tags = []
        let contentObj = {}
        if(data.content) {
          contentObj = JSON.parse(data.content)
          if(data.type === 10) {
            data.linkUrl = contentObj.linkUrl
          }
          if(contentObj.tags) {
            data.tags = contentObj.tags
          }
        }

        this.form = data;
        this.open = true;
        this.title = "修改运营区域";
      });
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status === 0 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '"吗?').then(function() {
          let params = { id: row.id, status: row.status }
          return updateSeoCardStatus(params);
        }).then(() => {
          this.$modal.msgSuccess(text + "成功");
        }).catch(function() {
          row.status = row.status === 1 ? 0: 1;
        });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        let contentObj = {}
        if(this.form.type === 10 && this.form.linkUrl) {
          contentObj.linkUrl = this.form.linkUrl
        } 
        if(this.form.tags) {
          contentObj.tags = this.form.tags
        }
        this.form.content = JSON.stringify(contentObj)

        // 修改的提交
        if (this.form.id != null) {
          updateSeoCard(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createSeoCard(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除运营区域编号为"' + id + '"的数据项?').then(function() {
          return deleteSeoCard(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    saveTags(tags = []) {
      this.form.tags = tags.map(tag => {
        return {
          id: tag.id,
          name: tag.name
        }
      })
    },
    handleTagClose(tag) {
      if(!this.form.tags || !this.form.tags.length) {
        return
      }
      let index = this.form.tags.findIndex(t => t.id === tag.id)
      this.form.tags.splice(index, 1);
    },
    addProductTag() {
      let tagIds = []
      if(this.form.tags) {
        tagIds = this.form.tags.map(tag => tag.id)
      }
      this.$refs.tagDialog.show(tagIds)
    }
  }
};
</script>
