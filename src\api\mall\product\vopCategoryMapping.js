import request from '@/utils/request'

// 创建京东分类映射
export function createVopCategoryMapping(data) {
  return request({
    url: '/mall/vop-category-mapping/create',
    method: 'post',
    data: data
  })
}

// 更新京东分类映射
export function updateVopCategoryMapping(data) {
  return request({
    url: '/mall/vop-category-mapping/update',
    method: 'put',
    data: data
  })
}

// 删除京东分类映射
export function deleteVopCategoryMapping(id) {
  return request({
    url: '/mall/vop-category-mapping/delete?id=' + id,
    method: 'delete'
  })
}

// 获得京东分类映射
export function getVopCategoryMapping(id) {
  return request({
    url: '/mall/vop-category-mapping/get?id=' + id,
    method: 'get'
  })
}

// 获得京东分类映射分页
export function getVopCategoryMappingPage(query) {
  return request({
    url: '/mall/vop-category-mapping/page',
    method: 'get',
    params: query
  })
}

// 导出京东分类映射 Excel
export function exportVopCategoryMappingExcel(query) {
  return request({
    url: '/mall/vop-category-mapping/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
