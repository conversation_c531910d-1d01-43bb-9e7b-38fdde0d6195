<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" class="order-form" size="small" :inline="true" v-show="showSearch"
      label-width="68px">
      <el-form-item label="用户名">
        <el-input v-model="queryParams.nickname" clearable></el-input>
      </el-form-item>
      <el-form-item label="关键字">
        <el-input v-model="queryParams.keyword" clearable></el-input>
      </el-form-item>
      <br>
      <el-form-item label="搜索时间" prop="updateTime">
        <el-date-picker v-model="queryParams.updateTime" value-format="timestamp" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
      <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
    </el-form>


    <el-table v-loading="loading" :data="list" border @sort-change="handleSortChange">
      <el-table-column label="用户名" align="center" prop="nickname"></el-table-column>
      <el-table-column label="搜索关键字" align="center" prop="keyword">
        <template v-slot="scope">
          <span :title="scope.row.keyword">{{ scope.row.keyword.length > 15 ? scope.row.keyword.substring(0, 15) + '...' :
      scope.row.keyword }}</span>
        </template>
      </el-table-column>
      <el-table-column label="搜索次数" align="center" prop="count" sortable></el-table-column>
      <el-table-column label="最近搜索时间" align="center" prop="updateTime">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                          v-hasPermi="['member:search-history:update']">修改</el-button> -->
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleDelete(scope.row)"
            v-hasPermi="['product:search-history:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

  </div>
</template>

<script>
import { getSearchHistoryPage, deleteSearchHistory } from "@/api/mall/member/searchHistory"
import DeptSelect from '@/views/mall/member/components/dept-select'
export default {
  name: "MemberSearchHistory",
  components: { DeptSelect },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      list: [],
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: '',
        keyword: '',
        count: '',
        updateTime: [],
        sortType: 0
      },
      title: "",
      open: false,
      form: {},
      submitLoading: false,
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      this.loading = true;
      const params = this.getParams();
      //修改为获取搜索记录
      const res = await getSearchHistoryPage(params);
      if (res.code === 0 && res.data) {
        this.list = res.data.list;
        this.total = Number(res.data.total);
      } else {
        this.list = []
        this.total = 0
      }
      this.loading = false;
    },
    getParams() {
      const params = {
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize,
        sortType: this.queryParams.sortType
      }
      if (this.queryParams.nickname) {
        params.nickname = this.queryParams.nickname
      }
      if (this.queryParams.keyword) {
        params.keyword = this.queryParams.keyword
      }
      if (this.queryParams.updateTime && this.queryParams.updateTime.length > 0) {
        params.updateTime = this.queryParams.updateTime
      }
      return params
    },
    handleSortChange({ column, prop, order }) {
      // 仅当排序字段是count时处理
      if (prop === 'count') {
        this.queryParams.sortType = order === 'ascending' ? 2 : 1;  // 升序=2，降序=1
      } else {
        this.queryParams.sortType = 0;  // 其他字段排序时恢复默认
      }
      this.queryParams.pageNo = 1;  // 排序后重置为第一页
      this.getList();  // 重新加载数据
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        nickname: '',
        keyword: '',
      }
      this.getList();
    },
    async handleDelete(row) {
      await this.$confirm('确认删除该条搜索记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      try {
        const res = await deleteSearchHistory(row.id); // 假设API需要传入id参数
        if (res.code === 0) {
          this.$message.success('删除成功');
          this.getList(); // 刷新列表
        } else {
          this.$message.error(res.msg || '删除失败');
        }
      } catch (e) {
        this.$message.error('删除失败');
        console.error('删除失败', e);
      }
    },
  }
}
</script>

<style lang="scss" scoped></style>
